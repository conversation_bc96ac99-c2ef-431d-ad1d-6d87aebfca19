{"name": "cookie-signature", "version": "1.2.2", "main": "index.js", "description": "Sign and unsign cookies", "keywords": ["cookie", "sign", "unsign"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/visionmedia/node-cookie-signature.git"}, "dependencies": {}, "engines": {"node": ">=6.6.0"}, "devDependencies": {"mocha": "*", "should": "*"}, "scripts": {"test": "mocha --require should --reporter spec"}}