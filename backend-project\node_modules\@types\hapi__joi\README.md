# Installation
> `npm install --save @types/hapi__joi`

# Summary
This package contains type definitions for @hapi/joi (https://github.com/hapijs/joi).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/hapi__joi.

### Additional Details
 * Last updated: Tu<PERSON>, 25 Feb 2020 00:30:06 GMT
 * Dependencies: none
 * Global values: none

# Credits
These definitions were written by [<PERSON>](https://github.com/Bartvds), [<PERSON>](https://github.com/laurence-myers), [<PERSON>](https://github.com/cglantschnig), [<PERSON>](https://github.com/David<PERSON>-SW), [<PERSON><PERSON><PERSON>](https://github.com/GaelMagnan), [<PERSON><PERSON><PERSON>](https://github.com/ralekna), [<PERSON>](https://github.com/schfkt), [<PERSON><PERSON>](https://github.com/rokoroku), [<PERSON>](https://github.com/dankraus), [<PERSON><PERSON>](https://github.com/wanganjun), [<PERSON><PERSON>](https://github.com/rafaelkallis), [<PERSON> <PERSON>](https://github.com/aconanlai), [Peter Thorson](https://github.com/zaphoyd), [Will Garcia](https://github.com/thewillg), [Simon Schick](https://github.com/<PERSON>Schick), [Alejandro Fernandez Haro](https://github.com/afharo), [Silas Rech](https://github.com/lenovouser), [Anand Chowdhary](https://github.com/AnandChowdhary), [Miro Yovchev](https://github.com/myovchev), [David Recuenco](https://github.com/RecuencoJones), [Frederic Reisenhauer](https://github.com/freisenhauer), [Stefan-Gabriel Muscalu](https://github.com/legraphista), and [Simcha Wood](https://github.com/SimchaWood).
