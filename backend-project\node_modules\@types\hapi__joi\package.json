{"name": "@types/hapi__joi", "version": "16.0.12", "description": "TypeScript definitions for @hapi/joi", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/Bartvds", "githubUsername": "Bartvds"}, {"name": "<PERSON>", "url": "https://github.com/laurence-myers", "githubUsername": "laurence-myers"}, {"name": "<PERSON>", "url": "https://github.com/cglantschnig", "githubUsername": "cglants<PERSON>nig"}, {"name": "<PERSON>-<PERSON>", "url": "https://github.com/DavidBR-SW", "githubUsername": "DavidBR-SW"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/GaelMagnan", "githubUsername": "GaelMagnan"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ralekna", "githubUsername": "ralekna"}, {"name": "<PERSON>", "url": "https://github.com/schfkt", "githubUsername": "schfkt"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rokoroku", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/dankraus", "githubUsername": "dank<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/wanganjun", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/rafael<PERSON>lis", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/aconanlai", "githubUsername": "aconanlai"}, {"name": "<PERSON>", "url": "https://github.com/zaphoyd", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/thewillg", "githubUsername": "thewillg"}, {"name": "<PERSON>", "url": "https://github.com/SimonSchick", "githubUsername": "Simon<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/afharo", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/lenovouser", "githubUsername": "lenovouser"}, {"name": "<PERSON>", "url": "https://github.com/AnandChowdhary", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/myov<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/RecuencoJones", "githubUsername": "RecuencoJones"}, {"name": "<PERSON>", "url": "https://github.com/freisenhauer", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/legraphista", "githubUsername": "leg<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/SimchaWood", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/hapi__joi"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "24fb4d183126380b3366a6ce3b6c4679e14bbf58920b4b08705b35201f4acc77", "typeScriptVersion": "2.8"}